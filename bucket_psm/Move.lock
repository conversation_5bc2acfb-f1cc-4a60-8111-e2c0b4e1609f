# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "13A11219DE3D4192853DC7FC0614AB612BC0E9394AFBAC1574E1026DD0BF810E"
deps_digest = "52B406A7A21811BEF51751CF88DA0E76DAEFFEAC888D4F4060B1A72BBE7D8D35"
dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "BucketV2Oracle", name = "BucketV2Oracle" },
  { id = "BucketV2USD", name = "BucketV2USD" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "Bridge"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/bridge" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "BucketV2Framework"
source = { local = "../bucket_framework" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "BucketV2Oracle"
source = { local = "../bucket_oracle" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "BucketV2Framework", name = "BucketV2Framework" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "BucketV2USD"
source = { local = "../bucket_usd" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "BucketV2Framework", name = "BucketV2Framework" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[[move.package]]
id = "SuiSystem"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/sui-system" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
]

[move.toolchain-version]
compiler-version = "1.55.0"
edition = "2024.beta"
flavor = "sui"

[env]

[env.testnet]
chain-id = "4c78adac"
original-published-id = "0xb818b22a88d614c266c5f4436fb4447dee1c4fba8071c456f864851eb6dd194d"
latest-published-id = "0xb818b22a88d614c266c5f4436fb4447dee1c4fba8071c456f864851eb6dd194d"
published-version = "1"

[env.mainnet]
chain-id = "35834a8a"
original-published-id = "0xc2ae6693383e4a81285136effc8190c7baaf0e75aafa36d1c69cd2170cfc3803"
latest-published-id = "0xc2ae6693383e4a81285136effc8190c7baaf0e75aafa36d1c69cd2170cfc3803"
published-version = "1"
