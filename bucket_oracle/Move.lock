# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "37B9B0D901F5E16985C57215CB1FAF8238EBA03BE7F022308134633FA275B3F5"
deps_digest = "397E6A9F7A624706DBDFEE056CE88391A15876868FD18A88504DA74EB458D697"
dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "BucketV2Framework", name = "BucketV2Framework" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "Bridge"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/bridge" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "BucketV2Framework"
source = { local = "../bucket_framework" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[[move.package]]
id = "SuiSystem"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/sui-system" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
]

[move.toolchain-version]
compiler-version = "1.55.0"
edition = "2024.beta"
flavor = "sui"

[env]

[env.testnet]
chain-id = "4c78adac"
original-published-id = "0x589bc31d4f89f3fc2c8c94f78ba7b234992c06408f1a1571927c971cf8fcc0ce"
latest-published-id = "0x589bc31d4f89f3fc2c8c94f78ba7b234992c06408f1a1571927c971cf8fcc0ce"
published-version = "1"

[env.mainnet]
chain-id = "35834a8a"
original-published-id = "0xf2ab9aa60c5e879675351a1a89f47131de9dea7cc927327dd0e7282e295c7f5e"
latest-published-id = "0xf2ab9aa60c5e879675351a1a89f47131de9dea7cc927327dd0e7282e295c7f5e"
published-version = "1"
