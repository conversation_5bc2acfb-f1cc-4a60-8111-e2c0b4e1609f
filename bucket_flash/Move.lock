# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "AFA48A70DAB711E9D0D8EF635F83902B5820382CD999388859BE52299BAA09E8"
deps_digest = "397E6A9F7A624706DBDFEE056CE88391A15876868FD18A88504DA74EB458D697"
dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "BucketV2USD", name = "BucketV2USD" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "Bridge"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/bridge" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "BucketV2Framework"
source = { local = "../bucket_framework" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "BucketV2USD"
source = { local = "../bucket_usd" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "BucketV2Framework", name = "BucketV2Framework" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[[move.package]]
id = "SuiSystem"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/sui-system" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
]

[move.toolchain-version]
compiler-version = "1.55.0"
edition = "2024.beta"
flavor = "sui"

[env]

[env.testnet]
chain-id = "4c78adac"
original-published-id = "0x68d88be9921bd6730a0f1cdfc200a7e9dda6b3e862c0245cd3891511671bcb8c"
latest-published-id = "0x68d88be9921bd6730a0f1cdfc200a7e9dda6b3e862c0245cd3891511671bcb8c"
published-version = "1"

[env.mainnet]
chain-id = "35834a8a"
original-published-id = "0x0f51f9eb63574a1d12b62295599ac4f8231197f95b3cce9a516daba64f419d06"
latest-published-id = "0x0f51f9eb63574a1d12b62295599ac4f8231197f95b3cce9a516daba64f419d06"
published-version = "1"
