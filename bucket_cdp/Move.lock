# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "19AA8F26747EE07ADB36287CCE09833131B8C935D8F8B9881487CD324DCDF467"
deps_digest = "52B406A7A21811BEF51751CF88DA0E76DAEFFEAC888D4F4060B1A72BBE7D8D35"
dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "BucketV2Oracle", name = "BucketV2Oracle" },
  { id = "BucketV2USD", name = "BucketV2USD" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "Bridge"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/bridge" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "BucketV2Framework"
source = { local = "../bucket_framework" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "BucketV2Oracle"
source = { local = "../bucket_oracle" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "BucketV2Framework", name = "BucketV2Framework" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "BucketV2USD"
source = { local = "../bucket_usd" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "BucketV2Framework", name = "BucketV2Framework" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[[move.package]]
id = "SuiSystem"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "2cde80b5766b0bc2073908e10f6e3c81c93fd691", subdir = "crates/sui-framework/packages/sui-system" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
]

[move.toolchain-version]
compiler-version = "1.55.0"
edition = "2024.beta"
flavor = "sui"

[env]

[env.testnet]
chain-id = "4c78adac"
original-published-id = "0x801a162330af18f018022faf93d781e5f2777886cac46c269ba3cc09b808c59a"
latest-published-id = "0x801a162330af18f018022faf93d781e5f2777886cac46c269ba3cc09b808c59a"
published-version = "1"

[env.mainnet]
chain-id = "35834a8a"
original-published-id = "0x9f835c21d21f8ce519fec17d679cd38243ef2643ad879e7048ba77374be4036e"
latest-published-id = "0x9f835c21d21f8ce519fec17d679cd38243ef2643ad879e7048ba77374be4036e"
published-version = "1"
